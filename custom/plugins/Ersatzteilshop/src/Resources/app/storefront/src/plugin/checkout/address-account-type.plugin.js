import Plugin from 'src/plugin-system/plugin.class';

export default class AddressAccountTypePlugin extends Plugin {
    init() {
        this._registerEvents();
        this._initializeAccountType();
        this._initializeRegisterForm();
        this._initializePasswordToggle();
    }

    _registerEvents() {
        this._registerAccountTypeButtons();
        this._registerRegisterCheckbox();
        this._registerPasswordToggle();
    }

    /**
     * Setzt den richtigen Account-Typ beim <PERSON>den der Seite und passt die Buttons an.
     */
    _initializeAccountType() {
        const prefixElement = document.querySelector('.account-type-buttons');
        if (!prefixElement) return;

        const prefix = prefixElement.dataset.prefix || '';
        const inputField = document.getElementById(`${prefix}accountType`);
        const buttons = prefixElement.querySelectorAll(".account-type-button");

        if (!inputField || buttons.length === 0) return;

        // Setze Standardwert falls leer
        if (!inputField.value) {
            inputField.value = "private";
        }

        // Company fields visibility logic
        const companyFields = document.querySelectorAll('.js-field-toggle-contact-type-company');
        if (inputField.value === "business") {
            companyFields.forEach(field => field.classList.remove('d-none'));
        } else {
            companyFields.forEach(field => field.classList.add('d-none'));
        }

        // Aktiven Button setzen
        buttons.forEach(button => {
            if (button.getAttribute("data-value") === inputField.value) {
                button.classList.add("active");
            }
        });
    }

    /**
     * Reagiert auf Klicks der Account-Type-Buttons.
     */
    _registerAccountTypeButtons() {
        const prefixElement = document.querySelector('.account-type-buttons');
        if (!prefixElement) return;

        const prefix = prefixElement.dataset.prefix || '';
        const inputField = document.getElementById(`${prefix}accountType`);
        const buttons = prefixElement.querySelectorAll(".account-type-button");

        if (!inputField || buttons.length === 0) return;

        buttons.forEach(button => {
            button.addEventListener("click", function () {
                // 1. Buttons visuell aktualisieren
                buttons.forEach(btn => btn.classList.remove("active"));
                this.classList.add("active");

                // 2. AccountType setzen
                inputField.value = this.getAttribute("data-value");
                inputField.dispatchEvent(new Event("change"));

                // 3. Sichtbarkeit von Firma-Feldern steuern
                const companyFields = document.querySelectorAll('.js-field-toggle-contact-type-company');
                const prefix = 'billingAddress';
                if (this.getAttribute("data-value") === "business") {
                    companyFields.forEach(field => field.classList.remove('d-none'));

                    // 4. Setze "required" für das Firmenfeld, wenn Account-Typ "business"
                    const companyInputField = document.getElementById(`${prefix}company`);
                    if (companyInputField) {
                        companyInputField.setAttribute('required', 'required');
                    }
                } else {
                    companyFields.forEach(field => field.classList.add('d-none'));

                    // 5. Entferne "required", wenn Account-Typ nicht "business"
                    const companyInputField = document.getElementById(`${prefix}company`);
                    if (companyInputField) {
                        companyInputField.removeAttribute('required');
                    }
                }
            });
        });
    }

    _initializeRegisterForm() {
        const registerCheckbox = document.getElementById("createAccountOption");
        const registerFormGroup = document.querySelector(".js-register-form-group");
        if (!registerCheckbox || !registerFormGroup) return;

        registerFormGroup.style.display = registerCheckbox.checked ? "block" : "none";
    }

    _registerRegisterCheckbox() {
        const registerCheckbox = document.getElementById("createAccountOption");
        if (!registerCheckbox) return;

        registerCheckbox.addEventListener("change", () => {
            const registerFormGroup = document.querySelector(".js-register-form-group");
            if (registerFormGroup) {
                registerFormGroup.style.display = registerCheckbox.checked ? "block" : "none";
            }
        });
    }

    _initializePasswordToggle() {
        document.querySelectorAll(".toggle-password").forEach(icon => {
            const input = document.getElementById(icon.dataset.togglePassword);
            if (!input) return;

            const eyeClosed = icon.querySelector(".eye-closed");
            const eyeOpen = icon.querySelector(".eye-open");

            if (input.type === "password") {
                eyeClosed.classList.remove("hidden");
                eyeOpen.classList.add("hidden");
            } else {
                eyeClosed.classList.add("hidden");
                eyeOpen.classList.remove("hidden");
            }
        });
    }

    _registerPasswordToggle() {
        document.querySelectorAll(".toggle-password").forEach(icon => {
            icon.addEventListener("click", function () {
                const input = document.getElementById(this.dataset.togglePassword);
                if (!input) return;

                const eyeClosed = this.querySelector(".eye-closed");
                const eyeOpen = this.querySelector(".eye-open");

                input.type = input.type === "password" ? "text" : "password";
                eyeClosed.classList.toggle("hidden");
                eyeOpen.classList.toggle("hidden");
            });
        });
    }
}
