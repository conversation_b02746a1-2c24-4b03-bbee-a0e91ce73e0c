import Plugin from 'src/plugin-system/plugin.class';

export default class FormValidationDebugPlugin extends Plugin {
    init() {
        this._registerEvents();
    }

    _registerEvents() {
        // Listen to all form fields for invalid events
        const formFields = this.el.querySelectorAll('input, select, textarea');
        
        formFields.forEach(field => {
            field.addEventListener('invalid', this._onFieldInvalid.bind(this), true);
        });

        // Listen to form submit
        this.el.addEventListener('submit', this._onFormSubmit.bind(this), true);
    }

    _onFieldInvalid(event) {
        console.log('🔴 INVALID FIELD DETECTED:', {
            field: event.target,
            name: event.target.name,
            id: event.target.id,
            value: event.target.value,
            required: event.target.required,
            validity: event.target.validity,
            validationMessage: event.target.validationMessage,
            type: event.target.type,
            tagName: event.target.tagName
        });
    }

    _onFormSubmit(event) {
        console.log('📝 FORM SUBMIT DETECTED');
        
        // Check all fields for validity
        const formFields = this.el.querySelectorAll('input, select, textarea');
        
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                console.log('❌ INVALID FIELD ON SUBMIT:', {
                    field: field,
                    name: field.name,
                    id: field.id,
                    value: field.value,
                    required: field.required,
                    validity: field.validity,
                    validationMessage: field.validationMessage
                });
            }
        });
    }
}
